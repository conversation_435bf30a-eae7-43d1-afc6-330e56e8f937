/**
 * @file config_manager.h
 * @brief TBox模组端配置管理模块头文件
 * @details 基于OpenWrt UCI系统的统一配置管理，支持动态加载、保存、热更新
 *          提供类型安全的配置访问接口，支持配置变更通知机制
 * <AUTHOR>
 * @date 2024年
 * @version 4
 */

#ifndef _CONFIG_MANAGER_H_
#define _CONFIG_MANAGER_H_

#include <stdbool.h>
#include <stdint.h>
#include <uci.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief TBox主配置结构体
 * @details 包含系统级别的基础配置参数，如启用状态、日志级别、内存限制等
 *          对应配置文件中的 [tbox] main 段
 */
typedef struct {
    bool enabled;                    /**< 系统启用状态：true=启用，false=禁用 */
    char log_level[16];             /**< 日志级别：debug/info/warning/error */
    uint32_t max_memory;            /**< 最大内存使用量(KB)，用于内存管理限制 */
} tbox_main_config_t;

/**
 * @brief 网络连接配置结构体
 * @details 包含4G/LTE网络连接相关的所有配置参数
 *          对应配置文件中的 [network] connection 段
 */
typedef struct {
    char apn[64];                   /**< 接入点名称(APN)，如"cmnet"、"3gnet"等 */
    char server_host[256];          /**< 服务器主机地址，支持IP或域名 */
    uint16_t server_port;           /**< 服务器端口号，通常为80803/ */
    bool auto_reconnect;            /**< 自动重连开关：true=启用，false=禁用 */
    int timeout;                    /**< 连接超时时间(秒)，默认30秒 */
} tbox_network_config_t;

/**
 * @brief 数据存储配置结构体
 * @details 包含数据存储、文件管理、压缩策略等配置参数
 *          对应配置文件中的 [data] storage 段
 */
typedef struct {
    char storage_path[256];         /**< 数据存储根路径，如"/usrdata/tbox" */
    uint32_t max_file_size;         /**< 单个文件最大大小(字节)，默认10MB */
    bool compression;               /**< 数据压缩开关：true=启用，false=禁用 */
    int retention_days;             /**< 数据保留天数，过期自动清理 */
} tbox_data_config_t;

/**
 * @brief TBox完整配置结构体
 * @details 整合所有配置子模块，提供统一的配置访问入口
 *          包含主配置、网络配置、数据配置三个子结构
 */
typedef struct {
    tbox_main_config_t main;        /**< 主配置参数 */
    tbox_network_config_t network;  /**< 网络配置参数 */
    tbox_data_config_t data;        /**< 数据存储配置参数 */
} tbox_config_t;

/**
 * @brief 配置变更回调函数类型定义
 * @details 当配置发生变更时，系统会调用此回调函数通知相关模块
 * @param section 配置段名称，如main"、"network、
 * @param option 配置项名称，如"enabled"、apn、server_host"
 * @param old_value 变更前的值，字符串格式
 * @param new_value 变更后的值，字符串格式
 */
typedef void (*config_change_callback_t)(const char *section, const char *option, 
                                        const char *old_value, const char *new_value);

/**
 * @brief 配置管理器核心结构体
 * @details 管理整个配置系统的生命周期，包括UCI上下文、配置缓存、状态跟踪等
 *          提供线程安全的配置访问和变更通知机制
 */
typedef struct {
    struct uci_context *ctx;        /**< UCI上下文指针，用于与OpenWrt配置系统交互 */
    struct uci_package *pkg;        /**< UCI配置包指针，指向当前加载的tbox配置包 */
    tbox_config_t config;           /**< 配置缓存，存储当前所有配置参数的结构化数据 */
    bool initialized;               /**< 初始化状态标志：true=已初始化，false=未初始化 */
    bool loaded;                    /**< 配置加载状态标志：true=已加载，false=未加载 */
    
    // 变更通知
    config_change_callback_t change_callback;  /**< 配置变更回调函数指针，可为NULL */
    
    // 统计信息
    uint32_t load_count;            /**< 配置加载次数统计，用于监控和调试 */
    uint32_t save_count;            /**< 配置保存次数统计，用于监控和调试 */
    uint32_t error_count;           /**< 配置操作错误次数统计，用于故障诊断 */
} config_manager_t;

/** @brief 全局配置管理器实例，应用程序中统一使用此实例 */
extern config_manager_t g_config_manager;

/**
 * @brief 配置管理器生命周期管理接口
 * @details 提供配置管理器的初始化、清理、加载、保存、重载等核心功能
 */

/**
 * @brief 初始化配置管理器
 * @param mgr 配置管理器指针，通常传入&g_config_manager
 * @return 0=成功，-1=失败
 * @details 分配UCI上下文，初始化内部数据结构，设置默认配置值
 */
int config_manager_init(config_manager_t *mgr);

/**
 * @brief 清理配置管理器
 * @param mgr 配置管理器指针
 * @return 0=成功，-1=失败
 * @details 释放UCI上下文，清理内部资源，重置状态标志
 */
int config_manager_cleanup(config_manager_t *mgr);

/**
 * @brief 从UCI配置文件加载配置
 * @param mgr 配置管理器指针
 * @return 0=成功，-1
 * @details 从/etc/config/tbox文件读取配置，解析到config结构体中
 *          如果文件不存在或读取失败，使用默认配置值
 */
int config_manager_load(config_manager_t *mgr);

/**
 * @brief 保存配置到UCI配置文件
 * @param mgr 配置管理器指针
 * @return 0=成功，-1失败
 * @details 将当前config结构体中的配置写入/etc/config/tbox文件
 *          会触发UCI系统的配置变更通知机制
 */
int config_manager_save(config_manager_t *mgr);

/**
 * @brief 重新加载配置文件
 * @param mgr 配置管理器指针
 * @return 0=成功，-1失败
 * @details 先卸载当前配置，然后重新从文件加载，用于热更新配置
 */
int config_manager_reload(config_manager_t *mgr);

/**
 * @brief 配置访问接口
 * @details 提供类型安全的配置项读取和设置功能，支持字符串、整数、布尔值类型
 */

/**
 * @brief 获取字符串类型配置项
 * @param mgr 配置管理器指针
 * @param section 配置段名称，如main"、"network、
 * @param option 配置项名称，如"log_level"、"apn"、"storage_path"
 * @param default_value 默认值，当配置项不存在时返回此值
 * @return 配置项的值，如果不存在返回default_value
 * @details 从UCI配置中读取字符串类型的配置项，支持默认值回退
 */
const char *config_manager_get_string(config_manager_t *mgr, const char *section, 
                                     const char *option, const char *default_value);

/**
 * @brief 获取整数类型配置项
 * @param mgr 配置管理器指针
 * @param section 配置段名称
 * @param option 配置项名称
 * @param default_value 默认值
 * @return 配置项的整数值，如果不存在或转换失败返回default_value
 * @details 从UCI配置中读取整数类型的配置项，自动进行字符串到整数的转换
 */
int config_manager_get_int(config_manager_t *mgr, const char *section, 
                          const char *option, int default_value);

/**
 * @brief 获取布尔类型配置项
 * @param mgr 配置管理器指针
 * @param section 配置段名称
 * @param option 配置项名称
 * @param default_value 默认值
 * @return 配置项的布尔值，如果不存在返回default_value
 * @details 支持多种布尔值格式："1"/"true/s"为true，"0"/false"/"no"为false
 */
bool config_manager_get_bool(config_manager_t *mgr, const char *section, 
                            const char *option, bool default_value);

/**
 * @brief 设置字符串类型配置项
 * @param mgr 配置管理器指针
 * @param section 配置段名称
 * @param option 配置项名称
 * @param value 要设置的值
 * @return 0=成功，-1=失败
 * @details 设置UCI配置项的值，会触发变更回调函数，但不会自动保存到文件
 */
int config_manager_set_string(config_manager_t *mgr, const char *section, 
                             const char *option, const char *value);

/**
 * @brief 设置整数类型配置项
 * @param mgr 配置管理器指针
 * @param section 配置段名称
 * @param option 配置项名称
 * @param value 要设置的整数值
 * @return 0=成功，-1=失败
 * @details 将整数值转换为字符串后设置到UCI配置中
 */
int config_manager_set_int(config_manager_t *mgr, const char *section, 
                          const char *option, int value);

/**
 * @brief 设置布尔类型配置项
 * @param mgr 配置管理器指针
 * @param section 配置段名称
 * @param option 配置项名称
 * @param value 要设置的布尔值
 * @return 0=成功，-1失败
 * @details 将布尔值转换为"1/"0"字符串后设置到UCI配置中
 */
int config_manager_set_bool(config_manager_t *mgr, const char *section, 
                           const char *option, bool value);

/**
 * @brief 便捷访问接口
 * @details 提供更高级的配置访问功能，如获取完整配置结构体、设置变更回调等
 */

/**
 * @brief 获取完整的配置结构体
 * @param mgr 配置管理器指针
 * @return 配置结构体指针，如果未加载返回NULL
 * @details 返回当前缓存的完整配置结构体，便于批量访问配置参数
 *          推荐优先使用此接口，避免频繁的字符串查找操作
 */
const tbox_config_t *config_manager_get_config(config_manager_t *mgr);

/**
 * @brief 设置配置变更回调函数
 * @param mgr 配置管理器指针
 * @param callback 回调函数指针，可为NULL表示取消回调
 * @return 0=成功，-1=失败
 * @details 注册配置变更通知回调函数，当配置发生变更时会自动调用
 *          支持动态更换回调函数，NULL表示取消通知
 */
int config_manager_set_change_callback(config_manager_t *mgr, config_change_callback_t callback);

/**
 * @brief 工具函数
 * @details 提供配置管理器的状态查询、统计信息获取等辅助功能
 */

/**
 * @brief 获取配置管理器统计信息
 * @param mgr 配置管理器指针
 * @param stats_buf 统计信息输出缓冲区
 * @param buf_size 缓冲区大小
 * @return 0=成功，-1=失败
 * @details 生成格式化的统计信息字符串，包含初始化状态、加载次数、错误次数等
 *          用于系统监控和故障诊断
 */
int config_manager_get_stats(config_manager_t *mgr, char *stats_buf, size_t buf_size);

/**
 * @brief 检查配置是否已加载
 * @param mgr 配置管理器指针
 * @return true=已加载，false=未加载
 * @details 检查配置管理器的loaded状态标志，用于判断配置是否可用
 */
bool config_manager_is_loaded(config_manager_t *mgr);

#ifdef __cplusplus
}
#endif

#endif // _CONFIG_MANAGER_H_