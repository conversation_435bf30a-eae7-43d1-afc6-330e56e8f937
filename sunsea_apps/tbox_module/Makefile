# TBox模组端基础架构 Makefile
# 基于v4.0架构文档从零设计

# 交叉编译工具链配置
CROSS_COMPILE ?= arm-openwrt-linux-muslgnueabi-
CC = $(CROSS_COMPILE)gcc
AR = $(CROSS_COMPILE)ar
STRIP = $(CROSS_COMPILE)strip
OBJDUMP = $(CROSS_COMPILE)objdump

# 项目配置
PROJECT_NAME = tbox_module
VERSION = 4.0.0

# 目录配置
SRC_DIR = src
INCLUDE_DIR = include
BUILD_DIR = build
DIST_DIR = dist

# 编译参数
CFLAGS = -Wall -Wextra -O2 -g
CFLAGS += -I$(INCLUDE_DIR)
# 添加Sunsea SDK头文件路径
CFLAGS += -I$(CURDIR)/../sunsea_crosscompile/sysroots/target-arm/usr/include
CFLAGS += -std=c99
CFLAGS += -D_GNU_SOURCE
CFLAGS += -DVERSION=\"$(VERSION)\"

# 依赖库 (基于文档4.3节配置)
LIBS = -lubus -lubox -luci -lblobmsg_json -ljson-c
LIBS += -lql_common_api -lql_peripheral -lql_nw -lql_gnss
LIBS += -lpthread -lm -lrt

# 链接参数
LDFLAGS = -Wl,--gc-sections

# 源文件配置
CORE_SOURCES = $(wildcard $(SRC_DIR)/core/*.c)
HAL_SOURCES = $(wildcard $(SRC_DIR)/hal/*.c)
MAIN_SOURCE = $(SRC_DIR)/main.c

ALL_SOURCES = $(CORE_SOURCES) $(HAL_SOURCES) $(MAIN_SOURCE)
ALL_OBJECTS = $(ALL_SOURCES:%.c=$(BUILD_DIR)/%.o)# 构建目标
.PHONY: all clean install test help debug release

# 默认目标
all: $(BUILD_DIR)/$(PROJECT_NAME)

# 创建构建目录
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)
	@mkdir -p $(BUILD_DIR)/src/core
	@mkdir -p $(BUILD_DIR)/src/hal
	@mkdir -p $(BUILD_DIR)/tests

# 编译对象文件
$(BUILD_DIR)/%.o: %.c | $(BUILD_DIR)
	@echo "Compiling $<..."
	@$(CC) $(CFLAGS) -c $< -o $@

# 链接主程序
$(BUILD_DIR)/$(PROJECT_NAME): $(ALL_OBJECTS)
	@echo "Linking $(PROJECT_NAME)..."
	@$(CC) $(LDFLAGS) -o $@ $^ $(LIBS)
	@echo "Build completed: $@"

# 调试版本
debug: CFLAGS += -DDEBUG -O0
debug: $(BUILD_DIR)/$(PROJECT_NAME)

# 发布版本
release: CFLAGS += -DNDEBUG -O3
release: $(BUILD_DIR)/$(PROJECT_NAME)
	@$(STRIP) $(BUILD_DIR)/$(PROJECT_NAME)
	@echo "Release build completed"

# 清理
clean:
	@echo "Cleaning build files..."
	@rm -rf $(BUILD_DIR)
	@rm -rf $(DIST_DIR)
	@echo "Clean completed"

# 安装
install: $(BUILD_DIR)/$(PROJECT_NAME)
	@echo "Installing $(PROJECT_NAME)..."
	@mkdir -p $(DESTDIR)/usr/bin
	@mkdir -p $(DESTDIR)/etc/config
	@mkdir -p $(DESTDIR)/etc/init.d
	@cp $(BUILD_DIR)/$(PROJECT_NAME) $(DESTDIR)/usr/bin/
	@cp config/tbox $(DESTDIR)/etc/config/
	@chmod +x $(DESTDIR)/usr/bin/$(PROJECT_NAME)
	@echo "Installation completed"

# 测试
test: $(BUILD_DIR)/$(PROJECT_NAME)
	@echo "Running tests..."
	@$(MAKE) -C tests
	@echo "Tests completed"

# 帮助信息
help:
	@echo "TBox Module Build System"
	@echo "Available targets:"
	@echo "  all      - Build the project (default)"
	@echo "  debug    - Build debug version"
	@echo "  release  - Build optimized release version"
	@echo "  clean    - Clean build files"
	@echo "  install  - Install to DESTDIR"
	@echo "  test     - Run unit tests"
	@echo "  help     - Show this help"
	@echo ""
	@echo "Variables:"
	@echo "  CROSS_COMPILE - Cross compiler prefix"
	@echo "  DESTDIR       - Installation destination"

# 显示编译信息
info:
	@echo "Build Configuration:"
	@echo "  Project: $(PROJECT_NAME) v$(VERSION)"
	@echo "  Compiler: $(CC)"
	@echo "  CFLAGS: $(CFLAGS)"
	@echo "  LIBS: $(LIBS)"
	@echo "  Sources: $(words $(ALL_SOURCES)) files"